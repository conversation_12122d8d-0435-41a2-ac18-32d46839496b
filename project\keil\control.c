#include "zf_common_headfile.h"

float yaw = 0;
float Gyro_get_values_zPal = 0;
uint8 straight_flag =0;
uint8 left_turn_flag = 0;
uint8 right_turn_flag = 0;
float expect_azimuth = 0;
uint8 change_count = 0;
uint8 zhuangtaiji = 0;
uint8 last_zhuangtaiji = 0;
uint8 last_last_zhuangtaiji = 0;
uint8 left_flag = 0;
uint8 right_flag = 0;
float position_error = 0;
float last_position_error = 0;
float error_output =0;
float kp = 28;
float kd = 8.0;
int16 expect_speed = 2000;
int16 pwm_left = 0;    //左电机
int16 pwm_right = 0;
int16 chasu =0;
int16 L_speed = 0;
int16 R_speed = 0;
uint8 stop_flag = 0;
uint8 InitFlag = 0;
uint8 time_count=0;
uint8 start_flag =0;
uint8 imu_menu_flag=0;
int ready_to_count_turn = 1;
uint16 counter = 0;
uint8 change_flag =1;
uint8 circle_count = 0;
uint8 total_count = 5;
uint8 stop_count_flag = 0;
uint8 catch_flag = 0;
uint8 first_road_judge_call = 0;

typedef enum
{
	STRAIGHT_ROAD = 0,
	TURN_LEFT =1,
	TURN_RIGHT = 2,
	DEVIATE_LEFT=3,  // 新增：向左偏移
    DEVIATE_RIGHT=4
}ROADSTATE;

//#define delta_T 0.005f // 5ms计算一次
#define alpha 0.3f
#define M_PI 3.1415926f

float GyroOffset_Xdata = 0, icm_data_acc_x = 0, icm_data_gyro_x = 0;
float GyroOffset_Ydata = 0, icm_data_acc_y = 0, icm_data_gyro_y = 0;
float GyroOffset_Zdata = 0, icm_data_acc_z = 0, icm_data_gyro_z = 0;
//float Q_info_q0 = 1, Q_info_q1 = 0, Q_info_q2 = 0, Q_info_q3 = 0;
//float param_Kp = 0.17;  // 加速度计的收敛速率比例增益
//float param_Ki = 0.004; // 陀螺仪收敛速率的积分增益 0.004
//float eulerAngle_yaw = 0, eulerAngle_pitch = 0, eulerAngle_roll = 0, eulerAngle_yaw_total = 0, eulerAngle_yaw_old = 0;

//float Err_Yaw =0;
//float Yaw =0;
//float Gyro_Yaw =0;
//float Filter_Weight = 0.005f;

float I_ex, I_ey, I_ez; // 误差积分

void  Gyro_Zero_drift_init() 
{
	  
		int i;
    system_delay_ms(500);
    for (i = 0; i < 1000; i++) 
	 {
				imu660rb_get_gyro();
        GyroOffset_Xdata += imu660rb_gyro_x;
		    GyroOffset_Ydata += imu660rb_gyro_y;
		    GyroOffset_Zdata += imu660rb_gyro_z;
        system_delay_ms(5);  // 间隔 10ms 采样
    }
    GyroOffset_Xdata /= 1000;
     GyroOffset_Ydata /= 1000;
     GyroOffset_Zdata /= 1000;
		InitFlag = 1;
}

void Gyro_get_rad_values()   //进行实际值的转化
{
	icm_data_acc_x = (((float)imu660rb_acc_x) * alpha) + icm_data_acc_x * (1 - alpha);
    icm_data_acc_y = (((float)imu660rb_acc_y) * alpha) + icm_data_acc_y * (1 - alpha);
    icm_data_acc_z = (((float)imu660rb_acc_z) * alpha) + icm_data_acc_z * (1 - alpha);
    // 陀螺仪角速度转弧度
    icm_data_gyro_x = ((float)imu660rb_gyro_x - GyroOffset_Xdata) * M_PI / 180 / 14.3f;
    icm_data_gyro_y = ((float)imu660rb_gyro_y - GyroOffset_Ydata) * M_PI / 180 / 14.3f;
    icm_data_gyro_z = ((float)imu660rb_gyro_z - GyroOffset_Zdata) * M_PI / 180 / 14.3f;
	Gyro_get_values_zPal = ((float)imu660rb_gyro_z -GyroOffset_Zdata) * PI / 180 / 14.3f;
}

void IMU_GetValues()//将采集的数值转化为实际物理值, 并对陀螺仪进行去零漂处理
{
//2000dps:IMU660--16.4
//2000dps:IMU963--14.3    角度制变为弧度制
//    float alpha=0.3;

        imu_data.gyro_x = ((float) imu660rb_gyro_x - GyroOffset_Xdata) * PI / 180 / 14.3f;
        imu_data.gyro_y = ((float) imu660rb_gyro_y - GyroOffset_Ydata) * PI / 180 / 14.3f;
        imu_data.gyro_z = ((float) imu660rb_gyro_z - GyroOffset_Zdata) * PI / 180 / 14.3f;

        if(imu_data.gyro_x<0.015&&imu_data.gyro_x>-0.015)
        {
            imu_data.gyro_x=0;
        }

        if(imu_data.gyro_y<0.015&&imu_data.gyro_y>-0.015)
        {
            imu_data.gyro_y=0;
        }

        if(imu_data.gyro_z<0.015&&imu_data.gyro_z>-0.015)
        {
            imu_data.gyro_z=0;
        }

//        imu_data.acc_x = ((float)imu963ra_acc_x/4098);
//        imu_data.acc_y = ((float)imu963ra_acc_y/4098);
//        imu_data.acc_z = ((float)imu963ra_acc_z/4098);
        //低通滤波，排除加速计的干扰

        imu_data.acc_x = (float)imu660rb_acc_x/4098*alpha+(1-alpha)*imu_data.acc_x;
        imu_data.acc_y = (float)imu660rb_acc_y/4098*alpha+(1-alpha)*imu_data.acc_y;
        imu_data.acc_z = (float)imu660rb_acc_z/4098*alpha+(1-alpha)*imu_data.acc_z;

}

void gyro_Zangle_get()
{
	//Gyro_get_rad_values();
	
	if(Gyro_get_values_zPal<0.015&&Gyro_get_values_zPal>-0.015)
    {
      yaw-=0;
    }
    else
    {
      yaw-=RAD_TO_ANGLE(Gyro_get_values_zPal*0.005);
    }
      if (yaw > 180)
      {
        yaw -= 360;
      }
      if (yaw < -180)
      {
        yaw += 360;
      }
}



/*void road_judge()
{
		left_flag = 0;
		right_flag = 0;
	if(((gpio_get_level(CIRCUIT_ONE) == 1)&&(gpio_get_level(CIRCUIT_TWO) == 0)&&(gpio_get_level(CIRCUIT_THREE)==0)&&(gpio_get_level(CIRCUIT_FOUR)==1))||
		((gpio_get_level(CIRCUIT_ONE) == 1)&&(gpio_get_level(CIRCUIT_TWO) == 1)&&(gpio_get_level(CIRCUIT_THREE)==0)&&(gpio_get_level(CIRCUIT_FOUR)==1)||
	(gpio_get_level(CIRCUIT_ONE) == 1)&&(gpio_get_level(CIRCUIT_TWO) == 0)&&(gpio_get_level(CIRCUIT_THREE)==1)&&(gpio_get_level(CIRCUIT_FOUR)==1)))
	{
		zhuangtaiji = STRAIGHT_ROAD;
	}else if((gpio_get_level(CIRCUIT_ONE) == 0)&&(gpio_get_level(CIRCUIT_TWO) == 0)&&(gpio_get_level(CIRCUIT_THREE)==0)&&(gpio_get_level(CIRCUIT_FOUR)==1))
	{
     zhuangtaiji = TURN_LEFT;
		change_count +=1;
		left_flag = 1;
		right_flag = 0;
	}else
	{
		zhuangtaiji = last_zhuangtaiji;
		if(zhuangtaiji == TURN_LEFT)
		{
			left_flag = 1;
		  right_flag = 0;
		}else 
		{
			left_flag = 0;
		  right_flag = 0;
		}
	}
	last_last_zhuangtaiji = last_zhuangtaiji;
	last_zhuangtaiji = zhuangtaiji;
	
}*/
/*if((gpio_get_level(CIRCUIT_ONE) == 1)&&(gpio_get_level(CIRCUIT_TWO) == 0)&&(gpio_get_level(CIRCUIT_THREE)==0)&&(gpio_get_level(CIRCUIT_FOUR)==0))
	{
		zhuangtaiji = TURN_RIGHT;
		left_flag = 0;
		right_flag = 1;
	}*/
/*void position_pid()  //转向环控制
{
	if(zhuangtaiji == TURN_LEFT)
	{
		
	}
	if(change_count%4==0)
	{
		expect_azimuth = 0;
	}
	
	if(change_count%4==1)
	{
		expect_azimuth = -90;
	}
	
	if(change_count%4==2)
	{
		if(left_flag == 1&&right_flag == 0)
		{
			expect_azimuth = -180;
		}else if(left_flag ==0&&right_flag==1)
		{
			expect_azimuth = 180;
		}
		expect_azimuth = -180;
	}
	
	if(change_count%4==3)
	{
		if(left_flag == 1&&right_flag == 0)
		{
			expect_azimuth = 90;
		}else if(left_flag ==0&&right_flag==1)
		{
			expect_azimuth = -90;
		}
		expect_azimuth = 90;
	}
	
	position_error =  expect_azimuth - yaw;
	if (position_error < -180)
      {
        position_error = position_error + 360;
      }
      else if (position_error > 180)
      {
        position_error = position_error - 360;
      }
	error_output = kp*position_error + kd*(position_error - last_position_error);
	last_position_error = position_error;
}*/
void road_judge()
{
    left_flag = 0;
    right_flag = 0;

    int s1 = gpio_get_level(CIRCUIT_ONE);
    int s2 = gpio_get_level(CIRCUIT_TWO);
    int s3 = gpio_get_level(CIRCUIT_THREE);
    int s4 = gpio_get_level(CIRCUIT_FOUR);
	
	if (first_road_judge_call == 1)
    {
        // 立刻清零标志位，确保此逻辑只执行一次
        first_road_judge_call = 0; 

        // 检查发车时是否直接处于左转状态
        if (((s1 == 1) && (s2 == 0) && (s3 == 0) && (s4 == 0)) ||
            ((s1 == 1) && (s2 == 1) && (s3 == 0) && (s4 == 0)) ||
            ((s1 == 0) && (s2 == 0) && (s3 == 0) && (s4 == 0)) ||
            ((s1 == 1) && (s2 == 1) && (s3 == 1) && (s4 == 0)))
        {
            // 如果是，则强制执行转弯逻辑，绕过常规判断
            zhuangtaiji = TURN_LEFT;
            change_count += 1;          // 强制转弯计数+1
            change_flag = 0;            // 立刻开始1.5秒的冷却计时
            ready_to_count_turn = 0;    // 我们刚转过弯，所以不是“准备好”状态
            left_flag = 1;

            last_last_zhuangtaiji = last_zhuangtaiji; // 更新状态历史
            last_zhuangtaiji = zhuangtaiji;
            
            // 重要：直接返回，避免执行下面的常规判断逻辑
            return; 
        }
    }

    // 1. 直道状态判断 (高优先级)
    if (((s1 == 1) && (s2 == 0) && (s3 == 0) && (s4 == 1)) ||
        ((s1 == 1) && (s2 == 1) && (s3 == 0) && (s4 == 1)) ||
        ((s1 == 1) && (s2 == 0) && (s3 == 1) && (s4 == 1)))
    {
        zhuangtaiji = STRAIGHT_ROAD;
        ready_to_count_turn = 1;
    }
    // 2. 左转状态判断
    else if (((s1 == 1) && (s2 == 0) && (s3 == 0) && (s4 == 0))||
			((s1 == 1)&&(s2 == 1)&&(s3 == 0)&&(s4==0))||
			((s1 == 0)&&(s2 == 0)&&(s3 == 0)&&(s4==0))||
		((s1 == 1)&&(s2 == 1)&&(s3 == 1)&&(s4==0)))
    {
        // MODIFIED: Only increment change_count if ready AND the cooldown has passed (change_flag is 1).
        if (ready_to_count_turn == 1 && change_flag == 1)
        {
            change_count += 1;
            ready_to_count_turn = 0; // Prevent multiple counts for the same physical turn
            change_flag = 0; 
        }
        zhuangtaiji = TURN_LEFT;
        left_flag = 1;
    }
    // 3. 【新增】右偏状态判断
    else if ((s1 == 0) && (s2 == 1) && (s3 == 1) && (s4 == 1))
    {
        zhuangtaiji = DEVIATE_RIGHT;
    }
    // 4. 【新增】左偏状态判断
    else if ((s1 == 1) && (s2 == 1) && (s3 == 1) && (s4 == 0))
    {
        zhuangtaiji = DEVIATE_LEFT;
    }
    // 5. 其他情况
    else
    {
        zhuangtaiji = last_zhuangtaiji;
        if (zhuangtaiji == TURN_LEFT)
        {
            left_flag = 1;
        }
    }
		if (change_count > 0 && (change_count % 4 == 0))
					{
						circle_count = change_count/4;
						
						if(circle_count==total_count)
						{
							if(change_flag == 1)
							{
								start_flag = 0;
								stop_flag = 1;
							}
						}
					}

    last_last_zhuangtaiji = last_zhuangtaiji;
    last_zhuangtaiji = zhuangtaiji;
}


/**
 *  位置式PID，用于转向角度控制
 * 根据转弯次数设定陀螺仪的目标角度
 */
void position_pid()
{
    // 这个函数主要负责大角度的转向控制，
    // DEVIATE_LEFT 和 DEVIATE_RIGHT 的微调通常不在这里处理。
    
    int mode = change_count % 4;

    if (mode == 0)      // 第1、5...个直道
    {
        expect_azimuth = 0;
    }
    else if (mode == 1) // 第2个直道
    {
        expect_azimuth = -90;
    }
    else if (mode == 2) // 第3个直道
    {
        expect_azimuth = -180;
    }
    else if (mode == 3) // 第4个直道
    {
        expect_azimuth = 90;
    }

    position_error = expect_azimuth - yaw_angle;
    if (position_error < -180)
    {
        position_error += 360;
    }
    else if (position_error > 180)
    {
        position_error -= 360;
    }

    error_output = kp * position_error + kd * (position_error - last_position_error);
    last_position_error = position_error;
}

int16 limit_ab(int16 x,int16 max,int16 min)
{
	if(x<max)
	{
		x = max;
	}
	if(x>min)
	{
		x = min;
	}
	return x;
}

void motor_output()   //直道限幅
{
	if(zhuangtaiji==STRAIGHT_ROAD)
	{
		error_output = limit_ab(error_output, -50,50);
		L_speed = expect_speed + error_output + chasu;
		R_speed = expect_speed - error_output -chasu;
	}else
	{
		L_speed = expect_speed + error_output + chasu;
		R_speed = expect_speed - error_output -chasu;
	}
	
	if(stop_flag)
	{
		L_speed = 0;
		R_speed = 0;
	}
	
	if(L_speed>5000)
	{
		L_speed = 5000;
	}
	if(L_speed <-5000)
	{
		L_speed = -5000;
	}
	if(R_speed>5000)
	{
		R_speed = 5000;
	}
	if(R_speed<-5000)
	{
		R_speed = -5000;
	}
	
	if(L_speed<0)
	{
		gpio_set_level(LEFT_GPIO,GPIO_HIGH);
		gpio_set_level(RIGHT_GPIO,GPIO_LOW);
		
		pwm_left = 0- L_speed;
		pwm_right = R_speed;
	}else if(R_speed <0)
	{
		gpio_set_level(LEFT_GPIO,GPIO_LOW);
		gpio_set_level(RIGHT_GPIO,GPIO_HIGH);
		
		pwm_left = L_speed;
		pwm_right = 0-R_speed;
	}else{
		gpio_set_level(LEFT_GPIO,GPIO_LOW);
		gpio_set_level(RIGHT_GPIO,GPIO_LOW);
		pwm_left = L_speed;
		pwm_right = R_speed;
	}
	
	pwm_set_duty(LEFT_PWM,pwm_left);
	pwm_set_duty(RIGHT_PWM,pwm_right);
}

void stop_car()
{
	tft180_clear();
	gpio_set_level(LEFT_GPIO,GPIO_LOW);
	gpio_set_level(RIGHT_GPIO,GPIO_LOW);
	pwm_set_duty(LEFT_PWM,0);
	pwm_set_duty(RIGHT_PWM,0);
}

float fast_sqrt(float num) 
{
    float halfx = 0.5f * num;
    float y = num;
    long i = *(long*)&y;
    i = 0x5f375a86 - (i >> 1);

    y = *(float*)&i;
    y = y * (1.5f - (halfx * y * y));
    y = y * (1.5f - (halfx * y * y));
    return y;

}

IMU_pid_param imu_pid_data={0.2,0.01,0.05};
IMU_param imu_data;
IMU_param filtered_data;

float dt=0.005;
float q0=1.0,q1=0,q2=0,q3=0;
float pitch=0;
float roll=0;
float yaw_angle = 0;
float pitch_last=0;
float roll_last=0;
float yaw_last=0;
float recipNorm=0.0;
float gravity_x=0,gravity_y=0,gravity_z=0;
float error_x=0,error_y=0,error_z=0;
float acc_error_x=0,acc_error_y=0,acc_error_z=0;

void smooth_kalman_filter(IMU_param *imu_data, IMU_param*filtered_data)
{
    static float x_hat = 0.0f;  // 初始状态估计
        static float P = 1.0f;      // 初始误差协方差
        float Q = 0.1f;             // 过程噪声协方差
        float R = 0.5f;             // 测量噪声协方差

        // 获取当前的陀螺仪数据
        float gyro = imu_data->gyro_x;  // 假设我们只滤波 gyro_x 数据

        // 预测步骤
        float x_hat_minus = x_hat;       // 上一时刻的估计值
        float P_minus = P + Q;           // 误差协方差的预测

        // 更新步骤
        float K = P_minus / (P_minus + R); // 卡尔曼增益
        x_hat = x_hat_minus + K * (gyro - x_hat_minus);  // 更新状态估计
        P = (1 - K) * P_minus;            // 更新误差协方差

        // 将滤波结果存入结构体
        filtered_data->gyro_x = x_hat;
        filtered_data->gyro_y = imu_data->gyro_y;  // 其它轴直接赋值
        filtered_data->gyro_z = imu_data->gyro_z;
        filtered_data->acc_x = imu_data->acc_x;
        filtered_data->acc_y = imu_data->acc_y;
        filtered_data->acc_z = imu_data->acc_z;
}
float invSqrt(float x)   //求开方的倒数
{
    float halfx = 0.5f*x;
    float y=x;
    long i=*(long*)&y;
    i=0x5f3759df -(i>>1);
    y=*(float*)&i;
    y = y*(1.5f - (halfx*y*y));
    return y;
}

void imu_attitude_algorithm()  //陀螺仪的姿态解算
{
    smooth_kalman_filter(&imu_data, &filtered_data);   //平滑数据
    float g1, g2, g3, g4, g5;
    float recipNorm;

    // 计算加速度的模长，并归一化加速度数据
    recipNorm = invSqrt(filtered_data.acc_x * filtered_data.acc_x + filtered_data.acc_y * filtered_data.acc_y + filtered_data.acc_z * filtered_data.acc_z);
    filtered_data.acc_x *= recipNorm;
    filtered_data.acc_y *= recipNorm;
    filtered_data.acc_z *= recipNorm;

//    // 检查陀螺仪角速度是否接近零，若接近零则认为没有明显旋转
//    float gyro_magnitude = sqrt(filtered_data.gyro_x * filtered_data.gyro_x + filtered_data.gyro_y * filtered_data.gyro_y + filtered_data.gyro_z * filtered_data.gyro_z);
//    if (gyro_magnitude < 0.01f) {  // 设定阈值（可以调整）
//        // 如果角速度非常小，认为设备没有旋转，直接返回，不更新姿态
//        return;
//    }
    if(filtered_data.gyro_z<0.015&&filtered_data.gyro_z>-0.015)    //z轴角速度变化很小就不更新姿态
    {
        pitch=pitch_last;
        roll=roll_last;
        yaw_angle=yaw_last;
    }else
    {
       // 计算重力方向
      gravity_x = 2 * (q1 * q3 - q0 * q2);
      gravity_y = 2 * (q0 * q1 + q2 * q3);
      gravity_z = 1 - 2 * q1 * q1 - 2 * q2 * q2;

      // 计算误差
      error_x = (filtered_data.acc_y * gravity_z - filtered_data.acc_z * gravity_y);
      error_y = (filtered_data.acc_z * gravity_x - filtered_data.acc_x * gravity_z);
      error_z = (filtered_data.acc_x * gravity_y - filtered_data.acc_y * gravity_x);

      acc_error_x = acc_error_x + error_x * imu_pid_data.ki * dt;
      acc_error_y = acc_error_y + error_y * imu_pid_data.ki * dt;
      acc_error_z = acc_error_z + error_z * imu_pid_data.ki * dt;

      // PID控制调整陀螺仪数据
     filtered_data.gyro_x = filtered_data.gyro_x + imu_pid_data.kp * error_x + acc_error_x;
     filtered_data.gyro_y = filtered_data.gyro_y + imu_pid_data.kp * error_y + acc_error_y;
     filtered_data.gyro_z = filtered_data.gyro_z + imu_pid_data.kp * error_z + acc_error_z;

     // 将陀螺仪数据转换为角速度
     filtered_data.gyro_x = filtered_data.gyro_x * (0.5f * dt);
     filtered_data.gyro_y = filtered_data.gyro_y * (0.5f * dt);
     filtered_data.gyro_z = filtered_data.gyro_z * (0.5f * dt);

     // 更新四元数
     q0 = q0 + (-q1 * filtered_data.gyro_x - q2 * filtered_data.gyro_y - q3 * filtered_data.gyro_z);
     q1 = q1 + (q0 * filtered_data.gyro_x + q2 * filtered_data.gyro_z - q3 * filtered_data.gyro_y);
     q2 = q2 + (q0 * filtered_data.gyro_y - q1 * filtered_data.gyro_y + q3 * filtered_data.gyro_x);
     q3 = q3 + (q0 * filtered_data.gyro_z + q1 * filtered_data.gyro_y - q2 * filtered_data.gyro_x);

     // 四元数归一化
     recipNorm = invSqrt(q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3);
     q0 = q0 * recipNorm;
     q1 = q1 * recipNorm;
     q2 = q2 * recipNorm;
     q3 = q3 * recipNorm;

     // 计算姿态（四元数到欧拉角）
     g1 = 2.0f * (q1 * q3 - q0 * q2);
     g2 = 2.0f * (q0 * q1 + q2 * q3);
     g3 = q0 * q0 - q1 * q1 - q2 * q2 + q3 * q3;
     g4 = 2.0f * (q1 * q2 + q0 * q3);
     g5 = q0 * q0 + q1 * q1 - q2 * q2 - q3 * q3;

     // 使用atan2来计算pitch, roll和yaw
     pitch = -atan2f(g1, g3) * 57.3f;  // 俯仰角
     roll = atan2f(g2, g3) * 57.3f;    // 滚转角
     yaw_angle = -atan2f(g4, g5) * 57.3f;    // 偏航角

//     if(straight_flag==2)
//     {
//         yaw=yaw-yaw_offset;      //掉头后陀螺仪的数值要减去偏差值
//     }

     // 确保角度在 -180° 到 180° 之间
     if (pitch > 180.0f) pitch -= 360.0f;
     if (pitch < -180.0f) pitch += 360.0f;

     if (roll > 180.0f) roll -= 360.0f;
     if (roll < -180.0f) roll += 360.0f;

     if (yaw_angle > 180.0f) yaw_angle -= 360.0f;
     if (yaw_angle < -180.0f) yaw_angle += 360.0f;

     pitch_last=pitch;
     roll_last=roll;
     yaw_last=yaw_angle;
    }
}

//void ICM_AHRSupdate(float gx, float gy, float gz, float ax, float ay, float az) {
//    float halfT = 0.5 * delta_T;
//    float vx, vy, vz; // 当前的机体坐标系上的重力单位向量
//    float ex, ey, ez; // 四元数计算值与加速度计测量值的误差
//    float q0 = Q_info_q0;
//    float q1 = Q_info_q1;
//    float q2 = Q_info_q2;
//    float q3 = Q_info_q3;
//    float q0q0 = q0 * q0;
//    float q0q1 = q0 * q1;
//    float q0q2 = q0 * q2;
//    //float q0q3 = q0 * q3;
//    float q1q1 = q1 * q1;
//    //float q1q2 = q1 * q2;
//    float q1q3 = q1 * q3;
//    float q2q2 = q2 * q2;
//    float q2q3 = q2 * q3;
//    float q3q3 = q3 * q3;
//    // float delta_2 = 0;

//    // 对加速度数据进行归一化 得到单位加速度
//    float norm = fast_sqrt(ax * ax + ay * ay + az * az);

//    ax = ax * norm;
//    ay = ay * norm;
//    az = az * norm;

//    // 根据当前四元数的姿态值来估算出各重力分量。用于和加速计实际测量出来的各重力分量进行对比，从而实现对四轴姿态的修正
//    vx = 2 * (q1q3 - q0q2);
//    vy = 2 * (q0q1 + q2q3);
//    vz = q0q0 - q1q1 - q2q2 + q3q3;
//    // vz = (q0*q0-0.5f+q3 * q3) * 2;

//    // 叉积来计算估算的重力和实际测量的重力这两个重力向量之间的误差。
//    ex = ay * vz - az * vy;
//    ey = az * vx - ax * vz;
//    ez = ax * vy - ay * vx;

//    // 用叉乘误差来做PI修正陀螺零偏，
//    // 通过调节 param_Kp，param_Ki 两个参数，
//    // 可以控制加速度计修正陀螺仪积分姿态的速度。
//    I_ex += halfT * ex; // integral error scaled by Ki
//    I_ey += halfT * ey;
//    I_ez += halfT * ez;

//    gx = gx + param_Kp * ex + param_Ki * I_ex;
//    gy = gy + param_Kp * ey + param_Ki * I_ey;
//    gz = gz + param_Kp * ez + param_Ki * I_ez;

//    /*数据修正完成，下面是四元数微分方程*/

//    // 四元数微分方程，其中halfT为测量周期的1/2，gx gy gz为陀螺仪角速度，以下都是已知量，这里使用了一阶龙哥库塔求解四元数微分方程
//        q0 = q0 + (-q1 * gx - q2 * gy - q3 * gz) * halfT;
//        q1 = q1 + (q0 * gx + q2 * gz - q3 * gy) * halfT;
//        q2 = q2 + (q0 * gy - q1 * gz + q3 * gx) * halfT;
//        q3 = q3 + (q0 * gz + q1 * gy - q2 * gx) * halfT;

//    // normalise quaternion
//    norm = fast_sqrt(q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3);
//    Q_info_q0 = q0 * norm;
//    Q_info_q1 = q1 * norm;
//    Q_info_q2 = q2 * norm;
//    Q_info_q3 = q3 * norm;

//}

//// 获取车辆姿态
//void ICM_getEulerianAngles(void) 
//{
//    // 采集陀螺仪数据
//    imu963ra_acc_x = imu963ra_acc_transition(imu963ra_acc_x);
//    imu963ra_acc_y = imu963ra_acc_transition(imu963ra_acc_y);
//    imu963ra_acc_z = imu963ra_acc_transition(imu963ra_acc_z);
//    Gyro_get_rad_values();
//    if(icm_data_gyro_z<0.015&&icm_data_gyro_z>-0.015)
//    {
//        eulerAngle_yaw = eulerAngle_yaw_old;
//    }else
//    {
//     ICM_AHRSupdate(icm_data_gyro_x, icm_data_gyro_y, icm_data_gyro_z, icm_data_acc_x, icm_data_acc_y, icm_data_acc_z);
//     float q0 = Q_info_q0;
//     float q1 = Q_info_q1;
//     float q2 = Q_info_q2;
//     float q3 = Q_info_q3;
//     // 四元数计算欧拉角---原始
//     eulerAngle_roll = -asin(-2 * q1 * q3 + 2 * q0 * q2) * 180 / M_PI;                                  // pitch
//     eulerAngle_pitch = -atan2(2 * q2 * q3 + 2 * q0 * q1, -2 * q1 * q1 - 2 * q2 * q2 + 1) * 180 / M_PI; // roll
//     eulerAngle_yaw = -atan2(2 * q1 * q2 + 2 * q0 * q3, -2 * q2 * q2 - 2 * q3 * q3 + 1) * 180 / M_PI;   // yaw
//     if (eulerAngle_roll < -180.0)
//     {
//         eulerAngle_roll += 360; // 如果俯仰角为负，调整为 0 到 360 范围
//     }else if(eulerAngle_roll>180.0)
//		 {
//			 eulerAngle_roll -=360;
//		 }

//     if (eulerAngle_pitch < -180.0)
//     {
//         eulerAngle_pitch += 360; // 如果滚转角为负，调整为 0 到 360 范围
//     }else if(eulerAngle_pitch>180.0)
//		 {
//			 eulerAngle_pitch -=360;
//		 }

//     if (eulerAngle_yaw < -180.0)
//     {
//         eulerAngle_yaw += 360; // 如果偏航角为负，调整为 0 到 360 范围
//     }else if(eulerAngle_yaw>180.0)
//		 {
//			 eulerAngle_yaw -=360;
//		 }
//     eulerAngle_yaw_old = eulerAngle_yaw;
//    }
//}