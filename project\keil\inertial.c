#include "zf_common_headfile.h"

#define INERTIAL_PAGE_INDEX   (8)     
float xr=0;
float yr=0;
float x_distance=0;
float y_distance=0;
uint8 inertial_count=0;
float coordinate[40][2];   
float iner_kp=2.5;
float iner_kd=1.4;
float error_iner=0;
float error_iner_last=0;
int16 iner_speed=1500;

void save_coordinate_array()    //存储点位信息
{
    coordinate[inertial_count][0] = xr;
    coordinate[inertial_count][1] = yr;
    inertial_count+=1;

    if(inertial_count>POINT_MAX)
    {
        inertial_count = 0;
    }
}

void save_inertial_data()   //将惯导信息存储到flash里面
{
	flash_buffer_clear();
	static int NUM=0;
  uint8 i=0;
	
	for(i=0;i<inertial_count;++i)
  {
        NUM = 2*i;

        flash_union_buffer[NUM].float_type =  coordinate[i][0];   //偶数存x坐标

        NUM = 2*i+1;

        flash_union_buffer[NUM].float_type = coordinate[i][1];    //奇数存y坐标
  }
	
	if(flash_check(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX))
	{
		flash_erase_page(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX);
		tft180_show_string(0,10,"win");
	}
	system_delay_ms(50);
  tft180_clear();
     flash_write_page_from_buffer(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX);
}

void read_inertial_data()    //读取惯导中的位置数据信息
{
    uint8 t=0;

    flash_buffer_clear();  //清空数据缓冲区

    flash_read_page_to_buffer(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX);
    for(t=0;t<POINT_MAX;++t)
    {
        coordinate[t][0]=flash_union_buffer[2*t].float_type;
        coordinate[t][1]=flash_union_buffer[2*t+1].float_type;
    }
    tft180_show_string(0,10,"finish");
    system_delay_ms(50);
    tft180_clear();
}

void clean_inertial_data()    //清除惯导中的数据信息
{
    flash_buffer_clear();  //清空数据缓冲区

    for (int j = 0; j <= 40; j++)  //清零数组
    {
        coordinate[j][0] = 0;
        coordinate[j][1] = 0;
        flash_union_buffer[j].float_type = 0;
    }
    if(flash_check(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX))
    {
        flash_erase_page(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX);
    }
    flash_write_page_from_buffer(FLASH_SECTION_INDEX,INERTIAL_PAGE_INDEX);
    inertial_count = 0;
    tft180_show_string(0,10,"clean");
    system_delay_ms(100);
    tft180_clear();
}

