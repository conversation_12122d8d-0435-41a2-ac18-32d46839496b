#include "zf_common_headfile.h"

uint8 n = 0;
uint8 n1 = 0;
uint8 n2 = 0;
uint8 n3 = 0;
uint8 d3 = 0;
uint8 d2 = 0;
uint8 d1 = 0;
uint8 servo_menu_flag = 0;
uint8 aim_menu_flag =0;

void arrows_move()    
{
    if(gpio_get_level(KEY_1)==GPIO_LOW)
        {
            system_delay_ms(50);
            n+=16;
            if(n>48)
            {
                n=0;
            }
            tft180_clear();
            system_delay_ms(150);
        }
}

void imu_arrows_move()//陀螺仪模块按键移动
{
	if(gpio_get_level(KEY_1)==GPIO_LOW)
        {
            system_delay_ms(50);
            n1+=16;
            if(n1>80)
            {
                n1=0;
            }
            tft180_clear();
            system_delay_ms(150);
        }
}

void servo_arrows_move()
{
	if(gpio_get_level(KEY_1)==GPIO_LOW)
        {
            system_delay_ms(50);
            n2+=16;
            if(n2>80)
            {
                n2=0;
            }
            tft180_clear();
            system_delay_ms(150);
        }
}

void aim_arrows_move()
{
	if(gpio_get_level(KEY_1)==GPIO_LOW)
        {
            system_delay_ms(50);
            n3+=16;
            if(n3>16)
            {
                n3=0;
            }
            tft180_clear();
            system_delay_ms(150);
        }
}

void aim_show()
{
	tft180_show_string(0,n3,"*");
	tft180_show_string(16,0,"mode1");
	tft180_show_string(16,16,"mode2");
}

void aim_operation()
{
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n3 == 0)
	{
		up_start_flag = 0;
		level_start_flag = 1;
		servo_180_output = mid_duty180;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n3==0)
	{
		up_start_flag = 0;
		level_start_flag = 0;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n3 == 16)
	{
		up_start_flag = 1;
		level_start_flag = 1;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n3==16)
	{
		up_start_flag = 0;
		level_start_flag = 0;
		tft180_clear();
    system_delay_ms(200);
	}
}

void imu_main()
{
	tft180_show_string(0,n1,"*");
	tft180_show_float(16,0,yaw_angle,3,6);
	tft180_show_string(16,16,"v");
	tft180_show_int(32,16,expect_speed,4);
	tft180_show_string(16,32,"kp");
	tft180_show_float(32,32,kp,3,6);
  tft180_show_string(16,48,"kd");
	tft180_show_float(32,48,kd,3,6);
	tft180_show_string(16,64,"num");
	tft180_show_uint(48,64,total_count,2);
}

void pid_adjustment()
{
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n1==16)
	{
		expect_speed+=50;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n1==16)
	{
		expect_speed-=50;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n1==32)
	{
		kp+=0.5;
    tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n1==32)
	{
		kp-=0.5;
		tft180_clear();
    system_delay_ms(200);	}
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n1==48)
	{
		kd +=0.5;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n1==48)
	{
		kd -=0.5;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_2)==GPIO_LOW&&n1==64)
	{
		total_count +=1;
		tft180_clear();
    system_delay_ms(200);
	}
	if(gpio_get_level(KEY_3)==GPIO_LOW&&n1==64)
	{
		total_count -=1;
		tft180_clear();
    system_delay_ms(200);
	}
}

void menu_main_show()
{
	tft180_show_string(0,n,"*");
	tft180_show_string(16,0,"menu");
	tft180_show_string(16,16,"imu");
	tft180_show_string(16,32,"servo");
	tft180_show_string(16,48,"aim");
	tft180_show_uint(16,64,gpio_get_level(CIRCUIT_ONE),4);
	tft180_show_uint(16,80,gpio_get_level(CIRCUIT_TWO),4);
	tft180_show_uint(16,96,gpio_get_level(CIRCUIT_THREE),4);
	tft180_show_uint(16,112,gpio_get_level(CIRCUIT_FOUR),4);
}


void menu_main()
{
	menu_main_show();
	arrows_move();
	
	switch(n)
	{
		case 16:
			if(gpio_get_level(KEY_2)==GPIO_LOW)
			{
				d1 = 1;
				tft180_clear();
				imu_menu_flag =1;		
        yaw = 0;		
        q0 = 1.0f;    //清四元数累积误差
                     q1 = 0.0f;
                     q2 = 0.0f;
                     q3 = 0.0f;
                     // 清除累积误差
                     acc_error_x = 0.0f;
                     acc_error_y = 0.0f;
                     acc_error_z = 0.0f;				
        system_delay_ms(200);
				while(d1)
				{
					imu_main();
					imu_arrows_move();
					pid_adjustment();
					if(gpio_get_level(KEY_4)==GPIO_LOW)
					{
						d1 = 0;
						imu_menu_flag = 0;
						tft180_clear();
            system_delay_ms(200);
					}
				}
			}
			break;
		case 32:
			if(gpio_get_level(KEY_2)==GPIO_LOW)
			{
				d2 = 1;
				servo_menu_flag = 1;
				tft180_clear();
				system_delay_ms(200);
				while(d2)
				{
					servo_show();
					servo_arrows_move();
					servo_adjustment();
					if(gpio_get_level(KEY_4)==GPIO_LOW)
					{
						d2 = 0;
						servo_menu_flag = 0;
						tft180_clear();
            system_delay_ms(200);
					}
				}
			}
			break;
		case 48:
			if(gpio_get_level(KEY_2)==GPIO_LOW)
			{
				d3 = 1;
				aim_menu_flag = 1;
				tft180_clear();
				system_delay_ms(200);
				while(d3)
				{
					aim_show();
					aim_arrows_move();
					aim_operation();
					if(gpio_get_level(KEY_4)==GPIO_LOW)
					{
						d3 = 0;
						aim_menu_flag = 0;
						tft180_clear();
            system_delay_ms(200);
					}
				}
			}
			break;
		default:break;
	}
}