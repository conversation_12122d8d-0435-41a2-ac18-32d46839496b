#include "zf_common_headfile.h"

uint16 mid_duty270 = 485;   //485   1320   175
uint16 mid_duty180 = 750;  //1200   260
uint16 up_counter = 0;
uint8 up_start_flag = 0;
uint16 level_counter = 0;
uint8 level_start_flag = 0;
uint8 up_stop_flag = 0;
uint8 level_stop_flag = 0;
uint16 servo_180_output = 750;
uint16 servo_270_output = 485;
uint8 flip_up_flag = 0;
uint8 flip_level_flag = 0;
uint8 up_increase_step = 5;
uint8 level_increase_step = 10;

void servo_adjustment()
{
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==0))
	{
		mid_duty270 += 1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==0))
	{
		mid_duty270 -= 1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==16))
	{
		mid_duty180 +=1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==16))
	{
		mid_duty180 -=1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==32))
	{
		up_start_flag = 1;
		tft180_clear();
		system_delay_ms(200);
	}
	
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==32))
	{
		up_start_flag = 0;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==48))
	{
		level_start_flag = 1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==48))
	{
		level_start_flag = 0;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==64))
	{
		up_increase_step +=1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==64))
	{
		up_increase_step -=1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_2)==GPIO_LOW)&&(n2==80))
	{
		level_increase_step +=1;
		tft180_clear();
		system_delay_ms(200);
	}
	if((gpio_get_level(KEY_3)==GPIO_LOW)&&(n2==80))
	{
		level_increase_step -=1;
		tft180_clear();
		system_delay_ms(200);
	}
//	pwm_set_duty(PWM_TIM_A1_CH1_B5,mid_duty180);
}

void servo_show()
{
	tft180_show_string(0,n2,"*");
	tft180_show_string(16,0,"270");
	tft180_show_uint(48,0,servo_270_output,4);
	tft180_show_string(16,16,"180");
	tft180_show_uint(48,16,servo_180_output,4);
	tft180_show_string(16,32,"up");
	tft180_show_string(16,48,"level");
	tft180_show_uint(16,64,up_increase_step,2);
	tft180_show_uint(16,80,level_increase_step,2);
}

void rotate_servo180()
{
	if(catch_flag)
	{
		return;
	}
	if(flip_up_flag==0)
	{
	servo_180_output +=up_increase_step;
	if(servo_180_output>=SERVO_180_MAX)
	{
		servo_180_output = SERVO_180_MAX;
		flip_up_flag = 1;
	}
	
  }else
	{
		servo_180_output -=up_increase_step;
		if(servo_180_output<=mid_duty180-50)
		{
			servo_180_output = mid_duty180-50;
			flip_up_flag = 0;
		}
	}
}

void rotate_servo270()
{
	if(catch_flag)
	{
		return;
	}
	if(flip_level_flag==0)
	{
		servo_270_output +=level_increase_step;
		if(servo_270_output>=SERVO_270_MAX)
		{
			servo_270_output = SERVO_270_MAX;
			flip_level_flag =1;
		}
		
	}else
	{
		servo_270_output -=level_increase_step;
		if(servo_270_output<=SERVO_270_MIN)
		{
			servo_270_output = SERVO_270_MIN;
			flip_level_flag = 0;
		}
		
	}
}