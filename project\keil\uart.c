#include "zf_common_headfile.h"
#include "uart.h"
// UART2数据接收相关变量定义
uint8 storageU2[4];   // 存储数据部分（4个字节）：data0（检测标志）、data1（x轴偏移量）、data2（测距低字节）、data3（测距高字节）
uint8 rx_art2[8];     // 存储完整的8字节数据帧：头帧2字节 + 数据部分4字节 + 尾帧2字节
uint8 rx_data2 = 0;   // 当前接收到的字节

// 数据范围保护相关变量
static uint32 uart2_error_count = 0;        // UART2错误计数器
static uint32 uart2_frame_count = 0;        // UART2帧计数器
static uint32 uart2_last_valid_time = 0;    // 上次有效数据时间戳
static uint32 uart2_interrupt_count = 0;    // UART2中断触发计数器（用于调试）

// 全局变量定义
int detection_flag = 0;     // 检测标志：1-检测到物体，0-未检测到
int center_x = 0;           // 矩形中心X轴坐标
int center_y = 0;           // 矩形中心Y轴坐标
int reserved_byte = 0;      // 保留字节
int uart2_data_received = 0; // UART2数据接收标志：1-收到数据，0-未收到数据
uint32 uart2_receive_count = 0; // UART2数据接收次数计数器（大数）

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     数据范围验证函数
// 参数说明     data            需要验证的数据
// 参数说明     min_val         最小允许值
// 参数说明     max_val         最大允许值
// 返回参数     uint8           1-数据有效，0-数据无效
// 使用示例     uart2_validate_data_range(data, 0, 255);
// 备注信息     用于验证接收数据是否在合理范围内
//-------------------------------------------------------------------------------------------------------------------
static uint8 uart2_validate_data_range(uint8 data, uint8 min_val, uint8 max_val)
{
    return (data >= min_val && data <= max_val) ? 1 : 0;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     帧头验证函数
// 参数说明     header1         帧头第一个字节
// 参数说明     header2         帧头第二个字节
// 返回参数     uint8           1-帧头有效，0-帧头无效
// 使用示例     uart2_validate_frame_header(0x2B, 0x11);
// 备注信息     验证数据帧头是否正确
//-------------------------------------------------------------------------------------------------------------------
static uint8 uart2_validate_frame_header(uint8 header1, uint8 header2)
{
    return (header1 == 0x2B && header2 == 0x11) ? 1 : 0;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     帧尾验证函数
// 参数说明     tail1           帧尾第一个字节
// 参数说明     tail2           帧尾第二个字节
// 返回参数     uint8           1-帧尾有效，0-帧尾无效
// 使用示例     uart2_validate_frame_tail(0x5A, 0x59);
// 备注信息     验证数据帧尾是否正确
//-------------------------------------------------------------------------------------------------------------------
static uint8 uart2_validate_frame_tail(uint8 tail1, uint8 tail2)
{
    return (tail1 == 0x5A && tail2 == 0x59) ? 1 : 0;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     数据完整性验证函数
// 参数说明     void
// 返回参数     uint8           1-数据有效，0-数据无效
// 使用示例     uart2_validate_data_integrity();
// 备注信息     验证接收到的完整数据帧是否有效
//-------------------------------------------------------------------------------------------------------------------
static uint8 uart2_validate_data_integrity(void)
{
    // 验证帧头
    if(!uart2_validate_frame_header(rx_art2[0], rx_art2[1]))
    {
        uart2_error_count++;
        return 0;
    }

    // 验证帧尾
    if(!uart2_validate_frame_tail(rx_art2[6], rx_art2[7]))
    {
        uart2_error_count++;
        return 0;
    }

    // 验证数据部分范围（根据实际协议调整范围）
    // 检测标志：应该是0或1
    if(!uart2_validate_data_range(rx_art2[2], 0, 1))
    {
        uart2_error_count++;
        return 0;
    }

    // X轴坐标：假设范围0-160（根据实际情况调整）
    if(!uart2_validate_data_range(rx_art2[3], 0, 160))
    {
        uart2_error_count++;
        return 0;
    }

    // Y轴坐标：假设范围0-120（根据实际情况调整）
    if(!uart2_validate_data_range(rx_art2[4], 0, 120))
    {
        uart2_error_count++;
        return 0;
    }

    // 保留字节：可以是任意值，暂不验证

    return 1; // 数据验证通过
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2初始化并启用接收中断
// 参数说明     void
// 返回参数     void
// 使用示例     uart2_init_with_interrupt();
// 备注信息     使用UART2_TX_B15和UART2_RX_B16引脚，波特率115200
//-------------------------------------------------------------------------------------------------------------------
void uart2_init_with_interrupt(void)
{
    // 初始化UART2，使用B15作为TX，B16作为RX，波特率115200
    uart_init(UART_2, 115200, UART2_TX_B17, UART2_RX_B18);

    // 启用UART2接收中断
    uart_set_interrupt_config(UART_2, UART_INTERRUPT_CONFIG_RX_ENABLE);

    // 设置UART2中断优先级
    interrupt_set_priority(UART2_INT_IRQn, 1);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2串口中断接收函数
// 参数说明     void
// 返回参数     void
// 使用示例     uart2_rx_interrupt_new_handler();
// 备注信息     在UART2中断服务函数中调用
//-------------------------------------------------------------------------------------------------------------------
void uart2_rx_interrupt_new_handler(void)
{
    // 中断触发计数器（无论是否有数据都会增加）
    uart2_interrupt_count++;

    // 查询接收一个字节，若有数据则存入 rx_data2
    if(uart_query_byte(UART_2, &rx_data2))
    {
        // 每次接收到数据，计数器加1
        uart2_receive_count++;

        // 数据范围基本验证（0-255已经由uint8保证）
        // 设置UART2数据接收标志
        uart2_data_received = 1;

        // 更新时间戳（使用简单计数器）
        uart2_last_valid_time++;

        // 调用接收函数，由其内部判断完整帧再调用 rt1064_storageU2()
        uart2_rx_art(rx_data2);
    }
    else
    {
        // 如果没有接收到有效数据，增加错误计数
        uart2_error_count++;

    if(uart2_error_count > 1000)
    {
        uart2_error_count = 0;
        // 重新初始化UART2
        uart_set_interrupt_config(UART_2, UART_INTERRUPT_CONFIG_RX_DISABLE);
        system_delay_ms(10);
        uart_set_interrupt_config(UART_2, UART_INTERRUPT_CONFIG_RX_ENABLE);
    }
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     UART2数据帧接收状态机（8字节数据帧）
// 参数说明     data            接收到的单个字节数据
// 返回参数     void
// 使用示例     uart2_rx_art(data);
// 备注信息     数据帧格式：0x2B 0x11 + 4字节数据 + 0x5A 0x59
//-------------------------------------------------------------------------------------------------------------------
void uart2_rx_art(uint8 data)
{
    static uint8 stage = 0;
    uint8 i;  // 循环变量，用于清空缓存数组

    switch(stage)
    {
        case 0: // 等待接收头帧1：0x2B
            if(data == 0x2B)
            {
                rx_art2[0] = data;
                stage = 1;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 1: // 等待接收头帧2：0x11
            if(data == 0x11)
            {
                rx_art2[1] = data;
                stage = 2;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 2: // 接收数据部分1：data0（检测标志）
            rx_art2[2] = data;
            stage = 3;
            break;

        case 3: // 接收数据部分2：data1（x轴偏移量）
            rx_art2[3] = data;
            stage = 4;
            break;

        case 4: // 接收数据部分3：data2（测距低字节）
            rx_art2[4] = data;
            stage = 5;
            break;

        case 5: // 接收数据部分4：data3（测距高字节）
            rx_art2[5] = data;
            stage = 6;
            break;

        case 6: // 等待接收尾帧1：0x5A
            if(data == 0x5A)
            {
                rx_art2[6] = data;
                stage = 7;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 7: // 等待接收尾帧2：0x59
            if(data == 0x59)
            {
                rx_art2[7] = data;
                // 完整接收到一帧数据，进行最终验证后调用数据处理函数
                if(uart2_validate_frame_header(rx_art2[0], rx_art2[1]) &&
                   uart2_validate_frame_tail(rx_art2[6], rx_art2[7]))
                {
                    rt1064_storageU2();
                }
                else
                {
                    // 帧验证失败，增加错误计数
                    uart2_error_count++;
                }
            }
            // 无论是否接收正确，状态都重置，并清空缓存数组
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;

        default:
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     从UART_2存储数据的函数
// 参数说明     void
// 返回参数     void
// 使用示例     rt1064_storageU2();
// 备注信息     处理接收到的完整数据帧，解析上位机发送的检测数据
//-------------------------------------------------------------------------------------------------------------------
void rt1064_storageU2(void)
{
    // 首先验证数据完整性
    if(!uart2_validate_data_integrity())
    {
        // 数据验证失败，不更新全局变量
        return;
    }

    // 数据验证通过，增加帧计数器
    uart2_frame_count++;

    // 将接收到的数据部分存入 storageU2 数组中
    storageU2[0] = rx_art2[2]; // 检测标志：1-检测到物体，0-未检测到
    storageU2[1] = rx_art2[3]; // 矩形中心X轴坐标
    storageU2[2] = rx_art2[4]; // 矩形中心Y轴坐标
    storageU2[3] = rx_art2[5]; // 保留字节

    // 数据范围二次验证和限制
    // 检测标志限制在0-1范围
    if(storageU2[0] > 1) storageU2[0] = 1;

    // X坐标限制在合理范围（根据实际屏幕分辨率调整）
    if(storageU2[1] > 160) storageU2[1] = 160;

    // Y坐标限制在合理范围（根据实际屏幕分辨率调整）
    if(storageU2[2] > 120) storageU2[2] = 120;

    // 更新全局变量，供后续逻辑使用
    detection_flag = storageU2[0];  // 检测标志
    center_x = storageU2[1];        // 中心X坐标
    center_y = storageU2[2];        // 中心Y坐标
    reserved_byte = storageU2[3];   // 保留字节
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     获取UART2错误统计信息
// 参数说明     void
// 返回参数     uint32          错误计数
// 使用示例     uint32 errors = uart2_get_error_count();
// 备注信息     用于监控UART2数据接收质量
//-------------------------------------------------------------------------------------------------------------------
uint32 uart2_get_error_count(void)
{
    return uart2_error_count;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     获取UART2帧统计信息
// 参数说明     void
// 返回参数     uint32          帧计数
// 使用示例     uint32 frames = uart2_get_frame_count();
// 备注信息     用于监控UART2数据接收数量
//-------------------------------------------------------------------------------------------------------------------
uint32 uart2_get_frame_count(void)
{
    return uart2_frame_count;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     重置UART2统计信息
// 参数说明     void
// 返回参数     void
// 使用示例     uart2_reset_statistics();
// 备注信息     清零错误计数和帧计数
//-------------------------------------------------------------------------------------------------------------------
void uart2_reset_statistics(void)
{
    uart2_error_count = 0;
    uart2_frame_count = 0;
    uart2_last_valid_time = 0;
    uart2_receive_count = 0;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     获取UART2接收次数
// 参数说明     void
// 返回参数     uint32          接收次数（大数）
// 使用示例     uint32 count = uart2_get_receive_count();
// 备注信息     返回UART2总的数据接收次数
//-------------------------------------------------------------------------------------------------------------------
uint32 uart2_get_receive_count(void)
{
    return uart2_receive_count;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     获取UART2中断触发次数
// 参数说明     void
// 返回参数     uint32          中断触发次数
// 使用示例     uint32 count = uart2_get_interrupt_count();
// 备注信息     返回UART2中断总的触发次数（用于调试）
//-------------------------------------------------------------------------------------------------------------------
uint32 uart2_get_interrupt_count(void)
{
    return uart2_interrupt_count;
}
