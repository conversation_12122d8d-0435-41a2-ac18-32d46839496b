  /*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          mian
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
********************************************************************************************************************/

#include "zf_common_headfile.h"
#include "../../keil/uart.h"

// 本例程是开源库空工程 可用作移植或者测试各类内外设
// 本例程是开源库空工程 可用作移植或者测试各类内外设
// 本例程是开源库空工程 可用作移植或者测试各类内外设

// **************************** 代码区域 ****************************
#define PIT_PRIORITY            (TIMA0_INT_IRQn)                                // 对应周期中断的中断编号 
uint8 pit_state = 0;
void pit_handler (uint32 state, void *ptr)
{
        pit_state = 1;                                                          // 周期中断触发 标志位置位
}

int main (void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();					// 调试串口信息初始化
	// 此处编写用户代码 例如外设初始化代码等
		tft180_init();   	
	
	while(1)
	{
		if(imu660rb_init())
		{
			tft180_show_string(0,10,"fail");
		}else
		{
			Gyro_Zero_drift_init();
			break;
		}
	}
  
	gpio_init(KEY_1,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(KEY_2,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(KEY_3,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(KEY_4,GPI,GPIO_HIGH,GPI_PULL_UP); 
	gpio_init(CIRCUIT_ONE,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(CIRCUIT_TWO,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(CIRCUIT_THREE,GPI,GPIO_HIGH,GPI_PULL_UP);
    gpio_init(CIRCUIT_FOUR,GPI,GPIO_HIGH,GPI_PULL_UP); 
	
	 gpio_init(LASER,GPO,GPIO_LOW,GPO_PUSH_PULL);
	
	gpio_init(LEFT_GPIO, GPO, GPIO_LOW, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
  pwm_init(LEFT_PWM, 17000, 0);    
  gpio_init(RIGHT_GPIO, GPO, GPIO_LOW, GPO_PUSH_PULL);                            // GPIO 初始化为输出 默认上拉输出高
  pwm_init(RIGHT_PWM, 17000, 0);  
	
	pwm_init(PWM_TIM_A1_CH0_B4,50,485);   //270    485
	pwm_init(PWM_TIM_A1_CH1_B5,50,750);   //180
	pit_ms_init(PIT_TIM_G0, 5, pit_handler, NULL);
	uart2_init_with_interrupt();
	interrupt_set_priority(PIT_PRIORITY, 2);
    // 此处编写用户代码 例如外设初始化代码等
    while(true)
    {
		// UART2看门狗检查
		uart2_watchdog_check();

		//	tft180_show_float(0,16,yaw,3,6);

		// 只有在主菜单状态下才显示这些数据，避免与子菜单冲突
		if(imu_menu_flag == 0 && servo_menu_flag == 0 && aim_menu_flag == 0)
		{
			tft180_show_int(50,0,detection_flag,4);
			tft180_show_int(50,16,center_x,4);
			tft180_show_int(50,32,center_y,4);
			// 根据UART2接收状态显示不同数值
			if(uart2_data_received)
			{
				tft180_show_uint(50,48,1,4);
				uart2_data_received = 0; // 清除标志，准备下次检测
			}
			else
			{
				tft180_show_uint(50,48,0,4);
			}

			// 显示UART2接收次数（大数显示）
			tft180_show_uint(50,64,uart2_get_receive_count(),5);  // 接收次数，显示10位数

			tft180_show_uint(50,80,uart2_get_error_count(),4);  // 错误计数
			tft180_show_uint(50,96,uart2_get_frame_count(),4);  // 帧计数
			tft180_show_uint(50,112,uart2_get_interrupt_count(),5);  // 中断计数
			tft180_show_uint(50,128,uart2_get_hardware_error_count(),3);  // 硬件错误计数
		}

    //   menu_main();

	// 	if(gpio_get_level(KEY_3)==GPIO_LOW&&imu_menu_flag==0&&servo_menu_flag==0&&aim_menu_flag==0)
	// 	{
	// 		tft180_clear();
	// 		change_count = 0;
	// 		circle_count = 0;
	// 		change_flag =1;
	// 		yaw = 0;
	// 		q0 = 1.0f;    //清四元数累积误差
    //                  q1 = 0.0f;
    //                  q2 = 0.0f;
    //                  q3 = 0.0f;
    //                  // 清除累积误差
    //                  acc_error_x = 0.0f;
    //                  acc_error_y = 0.0f;
    //                  acc_error_z = 0.0f;
			
	// 		first_road_judge_call = 1;
	// 		system_delay_ms(1000);
	// 		start_flag =1;
	// 		while(start_flag)
	// 		{
	// 			//tft180_full(RGB565_BLUE);
	// 			tft180_show_uint(0,0,pwm_left,4);
	// 			tft180_show_uint(0,16,pwm_right,4);
	// 			tft180_show_float(0,32,expect_azimuth,3,6);
	// 			tft180_show_float(0,48,yaw_angle,3,6);
	// 			if(gpio_get_level(KEY_4)==GPIO_LOW)
	// 			{
	// 				start_flag = 0;
	// 				tft180_clear();
	// 				system_delay_ms(200);
    //       while(gpio_get_level(KEY_4) == GPIO_LOW);
	// 			}
	// 		}
    //       tft180_clear();
	// 				system_delay_ms(100);
	// 	}
    //     // 此处编写需要循环执行的代码





		
    }
}


